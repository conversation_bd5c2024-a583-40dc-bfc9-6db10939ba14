import { z } from "zod";

export const profileSchema = z.object({
  first_name: z.string().min(2, "Voornaam moet minimaal 2 karakters bevatten"),
  last_name: z.string().min(2, "Achternaam moet minimaal 2 karakters bevatten"),
  email: z.string().email("Vul een geldig e-mailadres in"),
  city: z.string().min(1, "Vul een plaats in"),
  street_address: z.string().min(1, "Vul een straatnaam in"),
  house_number: z.string().min(1, "Vul een huisnummer in"),
  house_number_addition: z.string().optional(),
  phone_number: z
    .string()
    .regex(
      /^(\+31|0)6[\s-]?[1-9][0-9]{7}$/,
      "Vul een geldig Nederlands mobiel nummer in"
    )
    .optional(),
  company_name: z.string().optional(),
  user_type: z.enum(["vakman", "klusaanvrager"]),
  kvk_number: z
    .string()
    .regex(/^[0-9]{8}$/, "Vul een geldig KvK-nummer in (8 cijfers)")
    .optional(),
  btw_number: z
    .string()
    .regex(/^NL[0-9]{9}B[0-9]{2}$/, "Vul een geldig BTW-nummer in")
    .optional(),
  postal_code: z.string().min(1, "Voer een postcode in"),
});

export type ProfileFormData = z.infer<typeof profileSchema>;
