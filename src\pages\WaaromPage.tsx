import { <PERSON>, Timer, Star } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { Auth } from "@supabase/auth-ui-react";
import { ThemeSupa } from "@supabase/auth-ui-shared";

import { Button } from "@/components/ui/button";
import { AuthBenefitsSection } from "@/components/auth/sections/AuthBenefitsSection";
import { HowItWorksSection } from "@/components/auth/sections/HowItWorksSection";
import { StatisticsSection } from "@/components/auth/sections/StatisticsSection";
import { supabase } from "@/integrations/supabase/client";
import { FeaturesSection } from "@/components/werkwijze/FeaturesSection";
import { Alert, AlertDescription } from "@/components/ui/alert";

const SITE_URL = "https://klusgebied.nl";

const WaaromPage = () => {
  const navigate = useNavigate();
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === "SIGNED_IN" && session) {
        navigate("/");
      }
      if (event === "USER_UPDATED") {
        supabase.auth.getSession().then(({ error }) => {
          if (error) {
            console.error("Session error:", error);
            setErrorMessage(getErrorMessage(error));
          }
        });
      }
      if (event === "SIGNED_OUT") {
        setErrorMessage("");
      }
    });

    const checkSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session) {
        navigate("/");
      }
    };

    checkSession();
    return () => subscription.unsubscribe();
  }, [navigate]);

  const getErrorMessage = (error: any) => {
    if (error.message.includes("Email not confirmed")) {
      return "Bevestig je e-mailadres voordat je inlogt.";
    }
    if (error.message.includes("Invalid login credentials")) {
      return "Ongeldige inloggegevens. Controleer je e-mail en wachtwoord.";
    }
    if (error.message.includes("User not found")) {
      return "Geen gebruiker gevonden met deze gegevens.";
    }
    return "Er is een fout opgetreden. Probeer het opnieuw.";
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-muted/30 to-background">
      {/* Hero Section with Auth Form */}
      <div className="container mx-auto px-6 py-16 pb-32 max-w-5xl flex items-center justify-center min-h-[80vh]">
        <div className="w-full sm:max-w-[320px] auth-container">
          <div className="bg-card/80 backdrop-blur-sm px-6 py-8 rounded-lg shadow-2xl animate-fade-in w-full font-sans">
            {errorMessage && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{errorMessage}</AlertDescription>
              </Alert>
            )}
            <Auth
              supabaseClient={supabase}
              appearance={{
                theme: ThemeSupa,
                variables: {
                  default: {
                    colors: {
                      brand: "#EAB308",
                      brandAccent: "#CA8A04",
                    },
                  },
                },
                className: {
                  container: "auth-form-container",
                  button: "auth-button",
                  input: "auth-input",
                  anchor: "text-gray-600 hover:text-gray-900 no-underline",
                  message: "text-center text-sm text-gray-600",
                  divider: "text-center text-sm text-gray-600",
                },
              }}
              providers={[]}
              localization={{
                variables: {
                  sign_in: {
                    email_label: "E-mailadres",
                    password_label: "Wachtwoord",
                    email_input_placeholder: "Je e-mailadres",
                    password_input_placeholder: "Je wachtwoord",
                    button_label: "Inloggen",
                    loading_button_label: "Inloggen...",
                    link_text: "Wachtwoord vergeten?",
                  },
                  sign_up: {
                    email_label: "E-mailadres",
                    password_label: "Wachtwoord",
                    button_label: "Account aanmaken",
                    loading_button_label: "Account aanmaken...",
                    password_input_placeholder: "Kies een wachtwoord",
                    link_text: "Nog geen account? Registreer je hier",
                  },
                  forgotten_password: {
                    email_label: "E-mailadres",
                    button_label: "Stuur herstel instructies",
                    loading_button_label: "Versturen...",
                    link_text: "Wachtwoord vergeten?",
                  },
                },
              }}
              redirectTo={`${SITE_URL}/opnieuw_instellen`}
            />
          </div>
        </div>
      </div>

      {/* Main Benefits with Title */}
      <div className="container mx-auto px-4 py-12 pt-32 max-w-5xl">
        {/* Title Section */}
        <div className="text-center mb-16 animate-fade-in">
          <h1 className="font-display text-4xl md:text-5xl font-bold mb-6 tracking-tight leading-tight">
            Waarom Klusgebied?
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8 leading-relaxed font-light">
            Het beste platform voor het vinden van betrouwbare vakmannen en het
            laten uitvoeren van je klussen
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="p-6 bg-card rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 animate-fade-in [animation-delay:200ms] text-center">
            <p className="text-xl font-display font-semibold mb-3">
              Gegarandeerde Kwaliteit
            </p>
            <p className="text-muted-foreground leading-relaxed mb-6">
              Alle vakmannen worden zorgvuldig gescreend en beoordeeld door onze
              community. Zo weet je zeker dat je met een gekwalificeerde
              professional te maken hebt.
            </p>
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <Shield className="w-6 h-6 text-primary" />
            </div>
          </div>

          <div className="p-6 bg-card rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 animate-fade-in [animation-delay:400ms] text-center">
            <p className="text-xl font-display font-semibold mb-3">
              Perfecte Match
            </p>
            <p className="text-muted-foreground leading-relaxed mb-6">
              Vind de juiste vakman voor jouw specifieke klus. Filter op
              expertise, locatie en beschikbaarheid om de perfecte match te
              vinden.
            </p>
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <Timer className="w-6 h-6 text-primary" />
            </div>
          </div>

          <div className="p-6 bg-card rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 animate-fade-in [animation-delay:600ms] text-center">
            <p className="text-xl font-display font-semibold mb-3">
              Snelle Respons
            </p>
            <p className="text-muted-foreground leading-relaxed mb-6">
              Ontvang binnen 24 uur reacties van geïnteresseerde vakmannen. Geen
              lange wachttijden meer voor je klussen.
            </p>
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <Star className="w-6 h-6 text-primary" />
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <FeaturesSection />

      {/* Additional Sections */}
      <AuthBenefitsSection />
      <HowItWorksSection />
      <StatisticsSection />

      {/* CTA Section */}
      <div className="container mx-auto px-4 py-16 text-center max-w-5xl">
        <div className="max-w-3xl mx-auto bg-card p-8 rounded-lg shadow-lg animate-fade-in">
          <h2 className="text-3xl font-display font-bold mb-6">
            Klaar om je klus te laten uitvoeren?
          </h2>
          <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
            Plaats je klus vandaag nog en ontvang snel reacties van
            gekwalificeerde vakmannen.
          </p>
          <Button
            size="lg"
            onClick={() => navigate("/auth")}
            className="bg-primary hover:bg-primary/90 text-white px-8 py-6 text-lg animate-pulse"
          >
            Start nu gratis
          </Button>
        </div>
      </div>
    </div>
  );
};

export default WaaromPage;
