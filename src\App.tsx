import { Toaster } from "@/components/ui/toaster";
import { SessionProvider } from "@/components/auth/SessionProvider";
import { ProtectedRoutes } from "@/components/routing/ProtectedRoutes";
import { ScrollToTop } from "@/components/ScrollToTop";
import { JobsProvider } from "./contexts/JobsContext";

function App() {
  return (
    <>
      <ScrollToTop />
      <SessionProvider>
        {(session) => (
          <JobsProvider>
            <ProtectedRoutes session={session} />
          </JobsProvider>
        )}
      </SessionProvider>
      <Toaster />
    </>
  );
}

export default App;
